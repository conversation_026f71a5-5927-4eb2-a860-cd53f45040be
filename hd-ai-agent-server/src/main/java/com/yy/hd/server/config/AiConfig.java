package com.yy.hd.server.config;

import com.yy.hd.model.ChatClientSelector;
import com.yy.hd.model.ChatClientSelectorBuilder;
import com.yy.hd.model.advisor.RateLimiteAdvisor;
import com.yy.hd.model.properties.ModelContext;
import com.yy.hd.server.chat.advisor.LoggerAdvisor;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.model.tool.ToolCallingManager;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class AiConfig {

    @Bean
    @ConfigurationProperties(prefix = "spring.ai")
    public ModelContext modelContext() {
        return new ModelContext();
    }

    @Bean
    public ChatClientSelector chatClientSelector(ModelContext modelContext,
                                                 RateLimiteAdvisor rateLimiteAdvisor,
                                                 LoggerAdvisor loggerAdvisor) {
        List<Advisor> advisors = List.of(loggerAdvisor, rateLimiteAdvisor);
        return ChatClientSelectorBuilder.builder()
            .context(modelContext)
            .advisors(advisors)
            .build();
    }

}
