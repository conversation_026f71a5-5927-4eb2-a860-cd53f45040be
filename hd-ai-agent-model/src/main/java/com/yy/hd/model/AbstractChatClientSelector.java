package com.yy.hd.model;

import com.yy.hd.model.properties.Model;
import io.micrometer.observation.ObservationRegistry;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.boot.http.client.ClientHttpRequestFactoryBuilder;
import org.springframework.boot.http.client.ClientHttpRequestFactorySettings;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.web.client.RestClient;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

public abstract class AbstractChatClientSelector implements ChatClientSelector {

    private static final String DEFAULT_COMPLETIONS_PATH = "/v1/chat/completions";

    private static final ClientHttpRequestFactorySettings REQUEST_FACTORY_SETTINGS = ClientHttpRequestFactorySettings.defaults()
            .withConnectTimeout(Duration.ofSeconds(5))
            .withReadTimeout(Duration.ofSeconds(60));

    private static final HttpClient DEFAULT_HTTP_CLIENT = HttpClient.create()
            .responseTimeout(Duration.ofSeconds(60));

    private List<ChatClient> chatClients;

    protected ChatClient buildChatClient(String apiKey, String baseUrl, String completionsPath,
                                         Model model, RetryTemplate retryTemplate, List<Advisor> advisors) {
        if (chatClients == null) {
            chatClients = new ArrayList<>();
        }

        var openAiApi = OpenAiApi.builder()
                .apiKey(apiKey)
                .baseUrl(baseUrl)
                .restClientBuilder(RestClient.builder()
                        .requestFactory(ClientHttpRequestFactoryBuilder
                                .detect()
                                .build(REQUEST_FACTORY_SETTINGS)))
                .webClientBuilder(WebClient.builder()
                        .clientConnector(new ReactorClientHttpConnector(DEFAULT_HTTP_CLIENT)))
                .completionsPath(Optional.ofNullable(completionsPath).orElse(DEFAULT_COMPLETIONS_PATH))
                .build();
        var openAiChatOptions = OpenAiChatOptions.builder()
                .model(model.getModel())
                .temperature(model.getOptions().getTemperature())
                .build();
        ChatModel chatModel = OpenAiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(openAiChatOptions)
                .retryTemplate(retryTemplate)
                .observationRegistry(ObservationRegistry.create())
                .build();
        ChatClient chatClient = ChatClient.builder(chatModel)
                .defaultAdvisors(advisors)
                .build();
        chatClients.add(chatClient);
        return chatClient;
    }

    protected List<ChatClient> getChatClients() {
        return Collections.unmodifiableList(chatClients);
    }
}
