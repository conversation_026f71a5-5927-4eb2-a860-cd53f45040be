package com.yy.hd.web.deepresearch.controller;

import com.yy.hd.web.deepresearch.model.*;
import com.yy.hd.web.deepresearch.service.DeepResearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.CompletableFuture;

/**
 * 深度研究控制器
 * 提供深度研究相关的REST API
 */
@Slf4j
@RestController
@RequestMapping("/api/deepresearch")
@CrossOrigin(origins = "*")
public class DeepResearchController {
    
    @Autowired
    private DeepResearchService deepResearchService;
    
    /**
     * 开始深度研究
     * @param request 研究请求
     * @return 研究结果
     */
    @PostMapping("/start")
    public ResponseEntity<?> startResearch(@RequestBody ResearchRequest request) {
        try {
            log.info("Starting deep research for topic: {}", request.getTopic());
            
            // 验证请求参数
            if (request.getTopic() == null || request.getTopic().trim().isEmpty()) {
                return ResponseEntity.badRequest().body("研究主题不能为空");
            }
            
            // 开始异步研究
            CompletableFuture<ResearchResult> future = deepResearchService.startResearch(request);
            
            // 立即返回研究ID，不等待完成
            return future.thenApply(result -> ResponseEntity.ok(new StartResearchResponse(
                    result.getResearchId(),
                    "研究已开始",
                    result.getStatus()
            ))).exceptionally(throwable -> {
                log.error("Failed to start research", throwable);
                return ResponseEntity.internalServerError().body("启动研究失败: " + throwable.getMessage());
            }).join();
            
        } catch (Exception e) {
            log.error("Error starting research", e);
            return ResponseEntity.internalServerError().body("启动研究失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取研究结果
     * @param researchId 研究ID
     * @return 研究结果
     */
    @GetMapping("/result/{researchId}")
    public ResponseEntity<?> getResearchResult(@PathVariable String researchId) {
        try {
            return deepResearchService.getResearchResult(researchId)
                    .map(result -> ResponseEntity.ok(result))
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            log.error("Error getting research result: {}", researchId, e);
            return ResponseEntity.internalServerError().body("获取研究结果失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取研究进度
     * @param researchId 研究ID
     * @return 研究进度
     */
    @GetMapping("/progress/{researchId}")
    public ResponseEntity<?> getResearchProgress(@PathVariable String researchId) {
        try {
            return deepResearchService.getResearchProgress(researchId)
                    .map(progress -> ResponseEntity.ok(progress))
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            log.error("Error getting research progress: {}", researchId, e);
            return ResponseEntity.internalServerError().body("获取研究进度失败: " + e.getMessage());
        }
    }
    
    /**
     * 取消研究
     * @param researchId 研究ID
     * @return 取消结果
     */
    @PostMapping("/cancel/{researchId}")
    public ResponseEntity<?> cancelResearch(@PathVariable String researchId) {
        try {
            boolean cancelled = deepResearchService.cancelResearch(researchId);
            if (cancelled) {
                return ResponseEntity.ok(new CancelResearchResponse(researchId, "研究已取消"));
            } else {
                return ResponseEntity.badRequest().body("无法取消研究，可能研究不存在或已完成");
            }
        } catch (Exception e) {
            log.error("Error cancelling research: {}", researchId, e);
            return ResponseEntity.internalServerError().body("取消研究失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取研究图结构
     * @param researchId 研究ID
     * @return 研究图
     */
    @GetMapping("/graph/{researchId}")
    public ResponseEntity<?> getResearchGraph(@PathVariable String researchId) {
        try {
            return deepResearchService.getResearchResult(researchId)
                    .map(result -> {
                        if (result.getGraph() != null) {
                            return ResponseEntity.ok(result.getGraph());
                        } else {
                            return ResponseEntity.notFound().build();
                        }
                    })
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            log.error("Error getting research graph: {}", researchId, e);
            return ResponseEntity.internalServerError().body("获取研究图失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取研究节点详情
     * @param researchId 研究ID
     * @param nodeId 节点ID
     * @return 节点详情
     */
    @GetMapping("/node/{researchId}/{nodeId}")
    public ResponseEntity<?> getResearchNode(@PathVariable String researchId, @PathVariable String nodeId) {
        try {
            return deepResearchService.getResearchResult(researchId)
                    .map(result -> {
                        if (result.getGraph() != null) {
                            ResearchNode node = result.getGraph().getNode(nodeId);
                            if (node != null) {
                                return ResponseEntity.ok(node);
                            }
                        }
                        return ResponseEntity.notFound().build();
                    })
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            log.error("Error getting research node: {} - {}", researchId, nodeId, e);
            return ResponseEntity.internalServerError().body("获取研究节点失败: " + e.getMessage());
        }
    }
    
    /**
     * 清理完成的研究
     * @param hours 清理多少小时前的研究
     * @return 清理结果
     */
    @PostMapping("/cleanup")
    public ResponseEntity<?> cleanupResearch(@RequestParam(defaultValue = "24") int hours) {
        try {
            deepResearchService.cleanupCompletedResearch(hours);
            return ResponseEntity.ok(new CleanupResponse("清理完成", hours));
        } catch (Exception e) {
            log.error("Error cleaning up research", e);
            return ResponseEntity.internalServerError().body("清理失败: " + e.getMessage());
        }
    }
    
    /**
     * 开始研究响应
     */
    public record StartResearchResponse(
            String researchId,
            String message,
            ResearchResult.ResearchStatus status
    ) {}
    
    /**
     * 取消研究响应
     */
    public record CancelResearchResponse(
            String researchId,
            String message
    ) {}
    
    /**
     * 清理响应
     */
    public record CleanupResponse(
            String message,
            int hours
    ) {}
}
